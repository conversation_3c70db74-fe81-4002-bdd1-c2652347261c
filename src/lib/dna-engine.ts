export interface DNASequence {
  sessionId: string;
  sequence: string[];
  missingIndex: number;
  correctAnswer: string;
}

export interface DNASession {
  sessionId: string;
  sequence: string[];
  missingIndex: number;
  correctAnswer: string;
  startTime: number;
}

export interface DNAValidationResult {
  correct: boolean;
  points: number;
  message: string;
  correctAnswer?: string;
}

export class DNAGameEngine {
  private nucleotides = ['A', 'G', 'T', 'C'];
  private activeSessions: Map<string, DNASession> = new Map();

  generateDnaSequence(hostname?: string): DNASequence {
    const sessionId = this.generateSessionId();
    
    const length = Math.floor(Math.random() * 4) + 4; // 4-7 nucleotides total (including ?)
    const sequence = Array(length).fill(null).map(() => 
      this.nucleotides[Math.floor(Math.random() * this.nucleotides.length)]
    );

    const missingIndex = Math.floor(Math.random() * length);
    sequence[missingIndex] = '?';

    const correctAnswer = this.calculateCorrectAnswer(sequence, hostname);    

    // Store the session
    const session: DNASession = {
      sessionId,
      sequence,
      missingIndex,
      correctAnswer,
      startTime: Date.now()
    };
    
    this.activeSessions.set(sessionId, session);

    return {
      sessionId,
      sequence,
      missingIndex,
      correctAnswer
    };
  }

  validateDnaAnswer(sessionId: string, answer: string): DNAValidationResult {
    const session = this.activeSessions.get(sessionId);
    
    if (!session) {
      console.warn(`DNA validation failed: Session ${sessionId} not found`);
      return {
        correct: false,
        points: -1,
        message: "Session not found"
      };
    }

    if (!this.nucleotides.includes(answer)) {
      // Clean up the session on failure
      this.activeSessions.delete(sessionId);
      
      return {
        correct: false,
        points: -1,
        message: `Invalid nucleotide: ${answer}`,
        correctAnswer: session.correctAnswer
      };
    }
    
    const isCorrect = answer === session.correctAnswer;
    
    if (!isCorrect) {
      // Clean up the session on failure
      this.activeSessions.delete(sessionId);
      
      return {
        correct: false,
        points: -1,
        message: `Wrong nucleotide! Expected ${session.correctAnswer}`,
        correctAnswer: session.correctAnswer
      };
    }
    
    // Clean up the session on success
    this.activeSessions.delete(sessionId);
    
    return {
      correct: true,
      points: 3,
      message: "DNA sequence completed successfully!"
    };
  }

  // Legacy method for backward compatibility
  generateSequence(): DNASequence {
    return this.generateDnaSequence();
  }

  // Legacy method for backward compatibility  
  validateAnswer(sequence: string[], answer: string): boolean {
    if (!this.nucleotides.includes(answer)) return false;
    
    const correctAnswer = this.calculateCorrectAnswer(sequence);
    return answer === correctAnswer;
  }

  private generateSessionId(): string {
    return `dna_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  // Clean up old sessions (called periodically)
  cleanupOldSessions(maxAgeMs: number = 10 * 60 * 1000): void {
    const now = Date.now();
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now - session.startTime > maxAgeMs) {
        this.activeSessions.delete(sessionId);
      }
    }
  }

  // Get active session count for monitoring
  getActiveSessionCount(): number {
    return this.activeSessions.size;
  }

  private calculateCorrectAnswer(sequence: string[], hostname?: string): string {
    const length = sequence.length;
    const isNemo = hostname === 'nemo';
    
    if (length === 4) {
      return isNemo ? this.calculateNemo4PlusQuestionMark(sequence) : this.calculate3PlusQuestionMark(sequence);
    } else if (length === 5) {
      return isNemo ? this.calculateNemo5PlusQuestionMark(sequence) : this.calculate4PlusQuestionMark(sequence);
    } else if (length === 6) {
      return isNemo ? this.calculateNemo6PlusQuestionMark(sequence) : this.calculate5PlusQuestionMark(sequence);
    } else if (length === 7) {
      return isNemo ? this.calculateNemo7PlusQuestionMark(sequence) : this.calculate6PlusQuestionMark(sequence);
    }
    
    return sequence[sequence.length - 1];
  }

  private calculate3PlusQuestionMark(sequence: string[]): string {
    // Real nucleotides for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const gCount = realNucleotides.filter(n => n === 'G').length;
    const lastActualNucleotide = realNucleotides[realNucleotides.length - 1];
    
    if (aCount === 0) {
      return realNucleotides[1]; // second nucleotide excluding '?'
    } else if (lastActualNucleotide === 'C') {
      return lastActualNucleotide; // last nucleotide excluding '?'
    } else if (gCount > 1) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else {
      return lastActualNucleotide; // last nucleotide excluding '?'
    }
  }

  private calculate4PlusQuestionMark(sequence: string[]): string {
    // Real nucleotides for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const gCount = realNucleotides.filter(n => n === 'G').length;
    const lastRealNucleotide = realNucleotides[realNucleotides.length - 1];
    
    if (aCount > 1) {
      return lastRealNucleotide; // last nucleotide excluding '?'
    } else if (lastRealNucleotide === 'T' && aCount === 0) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else if (gCount === 1) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else if (tCount > 1) {
      return lastRealNucleotide; // last nucleotide excluding '?'
    } else {
      return realNucleotides[1]; // second nucleotide excluding '?'
    }
  }

  private calculate5PlusQuestionMark(sequence: string[]): string {
    // Real nucleotides for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const lastRealNucleotide = realNucleotides[realNucleotides.length - 1];

    if (lastRealNucleotide === 'C') {
      return realNucleotides[3]; // fourth nucleotide excluding '?'
    } else if (aCount === 1 && tCount > 1) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else if (cCount === 0) {
      return realNucleotides[1]; // second nucleotide excluding '?'
    } else {
      return realNucleotides[0]; // first nucleotide excluding '?'
    }
  }

  private calculate6PlusQuestionMark(sequence: string[]): string {
    // Filter out the '?' for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const lastRealNucleotide = realNucleotides[realNucleotides.length - 1];
    
    if (tCount === 0) {
      return realNucleotides[2]; // third nucleotide excluding '?'
    } else if (tCount === 1 && cCount > 1) {
      return realNucleotides[3]; // fourth nucleotide excluding '?'
    } else if (aCount === 0) {
      return lastRealNucleotide; // last nucleotide excluding '?'
    } else {
      return realNucleotides[3]; // fourth nucleotide excluding '?'
    }
  }

  // Nemo E.B variant DNA calculation methods
  private calculateNemo4PlusQuestionMark(sequence: string[]): string {
    const realNucleotides = sequence.filter(n => n !== '?');
    const gCount = realNucleotides.filter(n => n === 'G').length;
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const firstReal = realNucleotides[0];
    
    // S'il y a exactement deux G, répliquer le troisième nucléotide.
    if (gCount === 2) {
      return realNucleotides[2];
    }
    // Sinon, si le premier nucléotide est T et qu'il y a au moins un C, répliquer le premier nucléotide.
    else if (firstReal === 'T' && cCount >= 1) {
      return realNucleotides[0];
    }
    // Sinon, s'il n'y a aucun A, répliquer le troisième nucléotide.
    else if (aCount === 0) {
      return realNucleotides[2];
    }
    // Sinon, répliquer le second nucléotide.
    else {
      return realNucleotides[1];
    }
  }

  private calculateNemo5PlusQuestionMark(sequence: string[]): string {
    const realNucleotides = sequence.filter(n => n !== '?');
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const thirdReal = realNucleotides[2];
    const lastReal = realNucleotides[realNucleotides.length - 1];
    
    // Si le troisième nucléotide est C et qu'il y a plus d'un T, répliquer le premier nucléotide.
    if (thirdReal === 'C' && tCount > 1) {
      return realNucleotides[0];
    }
    // Sinon, s'il y a exactement trois A, répliquer le second nucléotide.
    else if (aCount === 3) {
      return realNucleotides[1];
    }
    // Sinon, si le dernier nucléotide est G et qu'il n'y a pas de T, répliquer le troisième nucléotide.
    else if (lastReal === 'G' && tCount === 0) {
      return realNucleotides[2];
    }
    // Sinon, s'il y a au moins deux C, répliquer le premier nucléotide.
    else if (cCount >= 2) {
      return realNucleotides[0];
    }
    // Sinon, répliquer le quatrième nucléotide.
    else {
      return realNucleotides[3];
    }
  }

  private calculateNemo6PlusQuestionMark(sequence: string[]): string {
    const realNucleotides = sequence.filter(n => n !== '?');
    const gCount = realNucleotides.filter(n => n === 'G').length;
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const secondReal = realNucleotides[1];
    const fifthReal = realNucleotides[4];
    const firstReal = realNucleotides[0];
    
    // S'il y a plus de deux G et que le second nucléotide est A, répliquer le quatrième nucléotide.
    if (gCount > 2 && secondReal === 'A') {
      return realNucleotides[3];
    }
    // Sinon, si le cinquième nucléotide est T et qu'il y a exactement un C, répliquer le premier nucléotide.
    else if (fifthReal === 'T' && cCount === 1) {
      return realNucleotides[0];
    }
    // Sinon, s'il n'y a aucun A et qu'il y a au moins un G, répliquer le second nucléotide.
    else if (aCount === 0 && gCount >= 1) {
      return realNucleotides[1];
    }
    // Sinon, si le premier nucléotide est C, répliquer le cinquième nucléotide.
    else if (firstReal === 'C') {
      return realNucleotides[4];
    }
    // Sinon, répliquer le troisième nucléotide.
    else {
      return realNucleotides[2];
    }
  }

  private calculateNemo7PlusQuestionMark(sequence: string[]): string {
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const gCount = realNucleotides.filter(n => n === 'G').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const fourthReal = realNucleotides[3];
    const sixthReal = realNucleotides[5];
    const lastReal = realNucleotides[realNucleotides.length - 1];
    
    // Si le quatrième nucléotide est A et qu'il y a exactement deux T, répliquer le premier nucléotide.
    if (fourthReal === 'A' && tCount === 2) {
      return realNucleotides[0];
    }
    // Sinon, s'il y a plus de trois G, répliquer le second nucléotide.
    else if (gCount > 3) {
      return realNucleotides[1];
    }
    // Sinon, si le sixième nucléotide est C et qu'il n'y a pas de A, répliquer le premier nucléotide.
    else if (sixthReal === 'C' && aCount === 0) {
      return realNucleotides[0];
    }
    // Sinon, s'il y a au moins trois C et que le dernier nucléotide est T, répliquer le cinquième nucléotide.
    else if (cCount >= 3 && lastReal === 'T') {
      return realNucleotides[4];
    }
    // Sinon, répliquer le quatrième nucléotide.
    else {
      return realNucleotides[3];
    }
  }
}

export const dnaGameEngine = new DNAGameEngine();
