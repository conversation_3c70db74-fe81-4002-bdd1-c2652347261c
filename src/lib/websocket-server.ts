import { WebSocketServer } from 'ws';
import { dnaGameEngine } from './dna-engine.js';
import { vaccineKeyEngine } from './vaccine-key-engine.js';

export interface WebSocketMessage {
  type: 'hint_created' | 'hint_dismissed' | 'alarm_started' | 'alarm_stopped' | 'scoreboard_updated' | 'session_created' | 'session_closed' | 'round_started' | 'round_ended' | 'answer_validation';
  hint?: {
    id: string;
    text: string;
    createdAt: string;
  };
  alarm?: {
    id: string;
    createdAt: string;
  };
  scoreboard?: {
    americas: number;
    europe: number;
    asia: number;
    updatedAt: string;
  };
  sessionData?: {
    team: string;
    sessionId?: string;
  };
  roundData?: {
    sessionId: string;
    roundId: string;
    gameState: {
      vaccineKeys: {
        keys: any[];
        pressedSequence: string[];
        keyStates: { [keyId: string]: 'normal' | 'success' | 'error' };
        status: 'idle' | 'active' | 'success' | 'error';
      };
      dnaScreen: {
        sequence: string[];
        status: 'idle' | 'active' | 'success' | 'error';
        feedback: {
          show: boolean;
          correct: boolean;
          points: number;
          message?: string;
        } | null;
      };
    };
    roundComplete: boolean;
    team?: string;
    nemoInfo?: {
      currentRound: number;
      maxRounds: number;
      isVictory: boolean;
    };
  };
  validationResult?: {
    sessionId: string;
    roundId: string;
    gameType: 'dna' | 'vaccine_key';
    succeeded: boolean;
    totalPoints: number;
    correctAnswer?: string;
    triggeredByModule: string;
    feedback?: {
      show: boolean;
      correct: boolean;
      points: number;
      message?: string;
    };
    keyStates?: { [keyId: string]: 'normal' | 'success' | 'error' };
  };
}

class GameWebSocketManager {
  private wss: WebSocketServer | null = null;
  private clients = new Set();
  private initialized = false;
  private teamSessions: {
    [key: string]: { 
      active: boolean; 
      sessionId: string | null;
      roundId: string | null;
      gameState: {
        vaccineKeys: {
          keys: any[];
          pressedSequence: string[];
          keyStates: { [keyId: string]: 'normal' | 'success' | 'error' };
          status: 'idle' | 'active' | 'success' | 'error';
        };
        dnaScreen: {
          sequence: string[];
          status: 'idle' | 'active' | 'success' | 'error';
          feedback: {
            show: boolean;
            correct: boolean;
            points: number;
            message?: string;
          } | null;
        };
      };
      roundComplete: boolean;
      moduleResults: { [gameType: string]: boolean };
      currentGameSessions: { vaccineKeySessionId?: string; dnaSessionId?: string };
      roundStartTime: number;
      nextRoundScheduled: boolean;
      nextRoundTimeout?: NodeJS.Timeout;
      processingMessage?: boolean;
      isNemo?: boolean;
      currentRound?: number;
      maxRounds?: number;
      isVictory?: boolean;
    }
  } = {
    americas: { 
      active: false, 
      sessionId: null, 
      roundId: null,
      gameState: {
        vaccineKeys: { keys: [], pressedSequence: [], keyStates: {}, status: 'idle' },
        dnaScreen: { sequence: [], status: 'idle', feedback: null }
      },
      roundComplete: false,
      moduleResults: {},
      currentGameSessions: {},
      roundStartTime: 0,
      nextRoundScheduled: false,
      processingMessage: false
    },
    eurafrica: { 
      active: false, 
      sessionId: null, 
      roundId: null,
      gameState: {
        vaccineKeys: { keys: [], pressedSequence: [], keyStates: {}, status: 'idle' },
        dnaScreen: { sequence: [], status: 'idle', feedback: null }
      },
      roundComplete: false,
      moduleResults: {},
      currentGameSessions: {},
      roundStartTime: 0,
      nextRoundScheduled: false,
      processingMessage: false
    },
    oceasia: { 
      active: false, 
      sessionId: null, 
      roundId: null,
      gameState: {
        vaccineKeys: { keys: [], pressedSequence: [], keyStates: {}, status: 'idle' },
        dnaScreen: { sequence: [], status: 'idle', feedback: null }
      },
      roundComplete: false,
      moduleResults: {},
      currentGameSessions: {},
      roundStartTime: 0,
      nextRoundScheduled: false,
      processingMessage: false
    },
    nemo: { 
      active: false, 
      sessionId: null, 
      roundId: null,
      gameState: {
        vaccineKeys: { keys: [], pressedSequence: [], keyStates: {}, status: 'idle' },
        dnaScreen: { sequence: [], status: 'idle', feedback: null }
      },
      roundComplete: false,
      moduleResults: {},
      currentGameSessions: {},
      roundStartTime: 0,
      nextRoundScheduled: false,
      processingMessage: false,
      isNemo: true,
      currentRound: 1,
      maxRounds: 1,
      isVictory: false
    }
  };

  initialize() {
    if (this.initialized || typeof window !== 'undefined') return;

    try {
      console.log('Initializing WebSocket server on port 3001...');
      
      // Auto-start nemo session (always active)
      this.createTeamSession('nemo');
      // Create WebSocket server on port 3001
      this.wss = new WebSocketServer({ 
        port: 3001,
        path: '/ws/games'
      });

      this.wss.on('connection', (ws) => {
        console.log('WebSocket client connected');
        this.clients.add(ws);

        ws.on('close', () => {
          console.log('WebSocket client disconnected');
          this.clients.delete(ws);
        });

        ws.on('error', (error) => {
          console.error('WebSocket error:', error);
          this.clients.delete(ws);
        });

        ws.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            this.handleIncomingMessage(ws, message);
          } catch (error) {
            console.error('Error parsing incoming WebSocket message:', error);
          }
        });

        // Send current active hint, alarm, scoreboard and session status on connection
        // Note: These are optional and should not prevent WebSocket functionality
        this.sendCurrentHint(ws).catch(err => console.log('Could not send current hint:', err));
        this.sendCurrentAlarm(ws).catch(err => console.log('Could not send current alarm:', err));
        this.sendCurrentScoreboard(ws).catch(err => console.log('Could not send current scoreboard:', err));
        this.sendCurrentSessionStatus(ws);
      });

      this.initialized = true;
      console.log('WebSocket server initialized on port 3001');
    } catch (error) {
      console.error('Failed to initialize WebSocket server:', error);
    }
  }

  private async sendCurrentHint(ws: any) {
    try {
      const { Pool } = await import('pg');
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL!,
      });

      const result = await pool.query(
        'SELECT * FROM hints WHERE "isActive" = true ORDER BY "createdAt" DESC LIMIT 1'
      );

      if (result.rows[0]) {
        ws.send(JSON.stringify({
          type: 'hint_created',
          hint: result.rows[0]
        }));
      }
    } catch (error) {
      console.error('Error sending current hint:', error);
    }
  }

  private async sendCurrentAlarm(ws: any) {
    try {
      const { Pool } = await import('pg');
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL!,
      });

      const result = await pool.query(
        'SELECT * FROM alarms WHERE "isActive" = true ORDER BY "createdAt" DESC LIMIT 1'
      );

      if (result.rows[0]) {
        ws.send(JSON.stringify({
          type: 'alarm_started',
          alarm: result.rows[0]
        }));
      }
    } catch (error) {
      console.error('Error sending current alarm:', error);
    }
  }

  broadcast(message: WebSocketMessage) {
    if (!this.wss) return;

    const messageStr = JSON.stringify(message);
    this.clients.forEach((ws: any) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(messageStr);
      }
    });
  }

  broadcastHintCreated(hint: any) {
    this.broadcast({
      type: 'hint_created',
      hint
    });
  }

  broadcastHintDismissed() {
    this.broadcast({
      type: 'hint_dismissed'
    });
  }

  broadcastAlarmStarted(alarm: any) {
    this.broadcast({
      type: 'alarm_started',
      alarm
    });
  }

  broadcastAlarmStopped() {
    this.broadcast({
      type: 'alarm_stopped'
    });
  }

  private async sendCurrentScoreboard(ws: any) {
    try {
      const { Pool } = await import('pg');
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL!,
      });

      const result = await pool.query(
        'SELECT * FROM scoreboard ORDER BY "updatedAt" DESC LIMIT 1'
      );

      if (result.rows[0]) {
        ws.send(JSON.stringify({
          type: 'scoreboard_updated',
          scoreboard: {
            americas: result.rows[0].americas,
            europe: result.rows[0].europe,
            asia: result.rows[0].asia,
            updatedAt: result.rows[0].updatedAt
          }
        }));
      }
    } catch (error) {
      console.error('Error sending current scoreboard:', error);
    }
  }

  broadcastScoreboardUpdate(scoreboard: { americas: number; europe: number; asia: number; updatedAt: string }) {
    this.broadcast({
      type: 'scoreboard_updated',
      scoreboard
    });
  }

  private async handleIncomingMessage(ws: any, message: any) {
    try {      
      // Rate limiting: prevent spam
      const now = Date.now();
      if (!ws.lastMessageTime) ws.lastMessageTime = 0;
      if (now - ws.lastMessageTime < 100) { // 100ms minimum between messages
        console.warn('Message rate limited');
        return;
      }
      ws.lastMessageTime = now;
      
      // Validate message format
      if (!message || typeof message !== 'object' || !message.type) {
        console.warn('Invalid message format:', message);
        return;
      }
      
      // Validate user's team before processing any game actions
      const userTeam = await this.validateUserTeam(ws, message);
      if (!userTeam) {
        console.warn('Invalid team validation for message:', message.type);
        return;
      }
      
      // Prevent concurrent processing of messages from the same team
      const session = this.teamSessions[userTeam as keyof typeof this.teamSessions];
      if (session.processingMessage) {
        console.warn(`Message ignored: Already processing message for team ${userTeam}`);
        return;
      }
      
      session.processingMessage = true;
      
      try {
        switch (message.type) {
          case 'submit_dna_answer':
            this.handleDnaAnswer(userTeam, message.sessionId, message.answer);
            break;
          case 'submit_vaccine_key_answer':
            this.handleVaccineKeyAnswer(userTeam, message.sessionId, message.answer);
            break;
          default:
            console.log('Unhandled message type:', message.type);
        }
      } finally {
        session.processingMessage = false;
      }
      
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }

  private async validateUserTeam(_ws: any, message: any): Promise<string | null> {
    try {
      // For WebSocket, we need to validate the user's session
      // Since WebSocket doesn't have easy access to HTTP session cookies,
      // we'll validate based on the message content and server state
      
      const claimedTeam = message.team;
      if (!claimedTeam || !['americas', 'eurafrica', 'oceasia', 'nemo'].includes(claimedTeam)) {
        console.warn('Invalid team claimed:', claimedTeam);
        return null;
      }
      
      // Verify the team has an active session
      if (!this.teamSessions[claimedTeam]?.active) {
        console.warn(`User tried to access inactive session for team: ${claimedTeam}`);
        return null;
      }
      
      // Additional validation: Check if the session ID matches
      if (message.sessionId && this.teamSessions[claimedTeam].sessionId !== message.sessionId) {
        console.warn(`Session ID mismatch for team ${claimedTeam}:`, 
          `claimed: ${message.sessionId}, actual: ${this.teamSessions[claimedTeam].sessionId}`);
        return null;
      }
      return claimedTeam;
    } catch (error) {
      console.error('Error validating user team:', error);
      return null;
    }
  }


  // Team session management methods
  createTeamSession(team: string): string {
    if (!['americas', 'eurafrica', 'oceasia', 'nemo'].includes(team)) {
      throw new Error('Invalid team');
    }

    const sessionId = `${team}-${Date.now()}`;
    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    session.active = true;
    session.sessionId = sessionId;

    // Broadcast session created
    this.broadcast({
      type: 'session_created' as any,
      sessionData: { team, sessionId }
    });
    
    // Start the first round
    this.startNewRound(team);
    
    return sessionId;
  }

  stopTeamSession(team: string): void {
    if (!['americas', 'eurafrica', 'oceasia', 'nemo'].includes(team)) {
      throw new Error('Invalid team');
    }

    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    session.active = false;
    session.sessionId = null;
    session.roundId = null;
    session.roundComplete = false;
    session.moduleResults = {};
    session.currentGameSessions = {};

    // Reset game state
    session.gameState.vaccineKeys = { keys: [], pressedSequence: [], keyStates: {}, status: 'idle' };
    session.gameState.dnaScreen = { sequence: [], status: 'idle', feedback: null };

    // Broadcast session closed
    this.broadcast({
      type: 'session_closed' as any,
      sessionData: { team }
    });
  }

  getSessionStatus() {
    return {
      americas: this.teamSessions.americas.active,
      eurafrica: this.teamSessions.eurafrica.active,
      oceasia: this.teamSessions.oceasia.active
    };
  }

  private sendCurrentSessionStatus(ws: any) {
    // Send session status for all teams
    Object.keys(this.teamSessions).forEach(team => {
      const session = this.teamSessions[team as keyof typeof this.teamSessions];
      if (session.active && session.sessionId) {
        // Send session created message for active sessions
        ws.send(JSON.stringify({
          type: 'session_created',
          sessionData: { team, sessionId: session.sessionId }
        }));
        
        // If there's an active round, send the current round state
        if (session.roundId && session.gameState) {
          ws.send(JSON.stringify({
            type: 'round_started',
            roundData: {
              sessionId: session.sessionId,
              roundId: session.roundId,
              gameState: session.gameState,
              roundComplete: session.roundComplete,
              team: team,
              nemoInfo: session.isNemo ? {
                currentRound: session.currentRound || 1,
                maxRounds: session.maxRounds || 5,
                isVictory: session.isVictory || false
              } : undefined
            }
          }));
        }
      }
    });
  }

  // Round management methods
  private startNewRound(team: string): void {
    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    if (!session.active || !session.sessionId) return;

    // Generate new round ID
    session.roundId = `${session.sessionId}-round-${Date.now()}`;
    session.roundComplete = false;
    session.moduleResults = {};
    session.roundStartTime = Date.now();
    session.nextRoundScheduled = false;
    
    // Clear any existing timeout
    if (session.nextRoundTimeout) {
      clearTimeout(session.nextRoundTimeout);
      session.nextRoundTimeout = undefined;
    }

    // Store game session IDs for this team (use hostname for nemo)
    const hostname = team === 'nemo' ? 'nemo' : undefined;
    const vaccineKeySequence = vaccineKeyEngine.generateVaccineKeySequence(hostname);
    const dnaSequence = dnaGameEngine.generateDnaSequence(hostname);
    
    session.currentGameSessions = {
      vaccineKeySessionId: vaccineKeySequence.sequenceId,
      dnaSessionId: dnaSequence.sessionId
    };
    
    // Initialize key states for persistence
    const keyStates: { [keyId: string]: 'normal' | 'success' | 'error' } = {};
    vaccineKeySequence.keys.forEach(key => {
      keyStates[key.id] = 'normal';
    });
    
    session.gameState = {
      vaccineKeys: {
        keys: vaccineKeySequence.keys,
        pressedSequence: [],
        keyStates: keyStates,
        status: 'active' as const
      },
      dnaScreen: {
        sequence: dnaSequence.sequence,
        status: 'active' as const,
        feedback: null
      }
    };

    // Broadcast round started
    this.broadcast({
      type: 'round_started',
      roundData: {
        sessionId: session.sessionId,
        roundId: session.roundId,
        gameState: session.gameState,
        roundComplete: session.roundComplete,
        team: team,
        nemoInfo: session.isNemo ? {
          currentRound: session.currentRound || 1,
          maxRounds: session.maxRounds || 5,
          isVictory: session.isVictory || false
        } : undefined
      }
    });
  }

  handleDnaAnswer(team: string, sessionId: string, answer: string): void {
    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    
    // Comprehensive validation
    if (!session.active) {
      console.warn(`DNA answer rejected: Session not active for team ${team}`);
      return;
    }
    
    if (session.sessionId !== sessionId) {
      console.warn(`DNA answer rejected: Session ID mismatch for team ${team}. Expected: ${session.sessionId}, Got: ${sessionId}`);
      return;
    }
    
    if (session.roundComplete) {
      console.warn(`DNA answer rejected: Round already complete for team ${team}`);
      return;
    }
    
    if (session.gameState.dnaScreen.status !== 'active') {
      console.warn(`DNA answer rejected: DNA screen not active for team ${team}, status: ${session.gameState.dnaScreen.status}`);
      return;
    }
    
    if (session.moduleResults['dna'] !== undefined) {
      console.warn(`DNA answer rejected: DNA module already completed for team ${team}`);
      return;
    }
    
    // Validate answer format
    if (!answer || typeof answer !== 'string' || !['A', 'C', 'G', 'T'].includes(answer.toUpperCase())) {
      console.warn(`DNA answer rejected: Invalid nucleotide '${answer}' for team ${team}`);
      return;
    }

    // Use actual DNA game engine for validation
    if (!session.currentGameSessions.dnaSessionId) {
      console.error('No DNA session ID available for validation');
      return;
    }

    const validation = this.validateDnaAnswer(session.currentGameSessions.dnaSessionId, answer);
    
    session.moduleResults['dna'] = validation.isCorrect;
    session.gameState.dnaScreen.status = validation.isCorrect ? 'success' : 'error';

    // Update DNA screen feedback state with server-calculated points
    const modulePoints = validation.isCorrect ? 3 : -1;
    session.gameState.dnaScreen.feedback = {
      show: true,
      correct: validation.isCorrect,
      points: modulePoints,
      message: validation.isCorrect ? 'Séquancage ADN complet !' : `Mauvais nucleotide ! Il fallait ${validation.correctAnswer}...`
    };

    // Send validation result with server-calculated points
    const currentRoundPoints = this.calculateRoundPoints(session.moduleResults);
    this.broadcast({
      type: 'answer_validation',
      validationResult: {
        sessionId: session.sessionId!,
        roundId: session.roundId!,
        gameType: 'dna',
        succeeded: validation.isCorrect,
        totalPoints: currentRoundPoints,
        correctAnswer: validation.isCorrect ? undefined : validation.correctAnswer,
        triggeredByModule: 'dna',
        feedback: session.gameState.dnaScreen.feedback
      }
    });

    this.checkRoundCompletion(team);
  }

  handleVaccineKeyAnswer(team: string, sessionId: string, answer: any): void {
    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    
    // Comprehensive validation
    if (!session.active) {
      console.warn(`Vaccine key answer rejected: Session not active for team ${team}`);
      return;
    }
    
    if (session.sessionId !== sessionId) {
      console.warn(`Vaccine key answer rejected: Session ID mismatch for team ${team}. Expected: ${session.sessionId}, Got: ${sessionId}`);
      return;
    }
    
    if (session.roundComplete) {
      console.warn(`Vaccine key answer rejected: Round already complete for team ${team}`);
      return;
    }
    
    if (session.gameState.vaccineKeys.status !== 'active') {
      console.warn(`Vaccine key answer rejected: Vaccine keys not active for team ${team}, status: ${session.gameState.vaccineKeys.status}`);
      return;
    }
    
    if (session.moduleResults['vaccine_key'] !== undefined) {
      console.warn(`Vaccine key answer rejected: Vaccine key module already completed for team ${team}`);
      return;
    }
    
    // Validate answer format
    if (!answer || !answer.keyId || !answer.symbol) {
      console.warn(`Vaccine key answer rejected: Invalid answer format for team ${team}:`, answer);
      return;
    }

    // Use actual vaccine key game engine for validation
    if (!session.currentGameSessions.vaccineKeySessionId) {
      console.error('No vaccine key session ID available for validation');
      return;
    }

    const validation = this.validateVaccineKeyAnswer(session.currentGameSessions.vaccineKeySessionId, answer);
    
    // Add to pressed sequence
    session.gameState.vaccineKeys.pressedSequence.push(answer.symbol);
    
    // Update the key state immediately for visual feedback (even if sequence not complete)
    if (session.gameState.vaccineKeys.keyStates[answer.keyId]) {
      // For now, set pressed keys to success for immediate feedback
      // This will be overridden if the final sequence is wrong
      session.gameState.vaccineKeys.keyStates[answer.keyId] = 'success';

      this.broadcast({
        type: 'answer_validation',
        validationResult: {
          sessionId: session.sessionId!,
          roundId: session.roundId!,
          gameType: 'vaccine_key',
          succeeded: false, // Not final result yet
          totalPoints: this.calculateRoundPoints(session.moduleResults),
          triggeredByModule: 'vaccine_key',
          keyStates: session.gameState.vaccineKeys.keyStates
        }
      });
    }
    
    if (validation.isCorrect !== null) {
      session.moduleResults['vaccine_key'] = validation.isCorrect;
      session.gameState.vaccineKeys.status = validation.isCorrect ? 'success' : 'error';
      
      // Update key states for visual feedback
      if (validation.isCorrect) {
        // Set all keys to success state
        Object.keys(session.gameState.vaccineKeys.keyStates).forEach(keyId => {
          session.gameState.vaccineKeys.keyStates[keyId] = 'success';
        });
      } else {
        // Set all keys to error state
        Object.keys(session.gameState.vaccineKeys.keyStates).forEach(keyId => {
          session.gameState.vaccineKeys.keyStates[keyId] = 'error';
        });
      }

      // Send validation result with server-calculated points and key states
      const currentRoundPoints = this.calculateRoundPoints(session.moduleResults);
      this.broadcast({
        type: 'answer_validation',
        validationResult: {
          sessionId: session.sessionId!,
          roundId: session.roundId!,
          gameType: 'vaccine_key',
          succeeded: validation.isCorrect,
          totalPoints: currentRoundPoints,
          correctAnswer: validation.correctAnswer ? validation.correctAnswer.join(', ') : undefined,
          triggeredByModule: 'vaccine_key',
          keyStates: session.gameState.vaccineKeys.keyStates
        }
      });

      this.checkRoundCompletion(team);
    }
  }

  private validateDnaAnswer(sessionId: string, answer: string): { isCorrect: boolean; correctAnswer?: string } {
    const result = dnaGameEngine.validateDnaAnswer(sessionId, answer);
    return {
      isCorrect: result.correct,
      correctAnswer: result.correctAnswer
    };
  }

  private validateVaccineKeyAnswer(sessionId: string, answer: any): { isCorrect: boolean | null; correctAnswer?: string[] } {
    const result = vaccineKeyEngine.validateKeyPress(sessionId, {
      keyId: answer.keyId,
      symbol: answer.symbol,
      sequence: answer.sequence
    });
    
    if (result.inProgress) {
      return { isCorrect: null }; // Still in progress
    }
    
    return {
      isCorrect: result.correct,
      correctAnswer: result.correctAnswer
    };
  }

  private calculateRoundPoints(moduleResults: { [gameType: string]: boolean }): number {
    // Server-side scoring: +3 for success, -1 for failure
    // If one module succeeds but round fails, the successful module keeps its +3 points
    let totalPoints = 0;
    for (const [gameType, succeeded] of Object.entries(moduleResults)) {
      if (succeeded) {
        totalPoints += 3; // +3 points for successful module
      } else {
        totalPoints -= 1; // -1 point for failed module
      }
    }

    return totalPoints;
  }

  private checkRoundCompletion(team: string): void {
    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    const moduleCount = 2; // DNA + VaccineKey
    
    // Check if any module failed and identify which one
    const failedModules = Object.keys(session.moduleResults).filter(module => session.moduleResults[module] === false);
    
    if (failedModules.length > 0) {
      // Cross-module failure propagation (pass the module that triggered the failure)
      const triggeringModule = failedModules[0]; // First module that failed
      this.propagateFailureToAllModules(team, triggeringModule);
      // Any failure ends the round immediately
      this.endRound(team, triggeringModule);
    } else if (Object.keys(session.moduleResults).length === moduleCount) {
      // All modules completed successfully
      this.endRound(team);
    }
  }
  
  private propagateFailureToAllModules(team: string, triggeringModule?: string): void {
    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    
    // Set all modules to error state
    session.gameState.vaccineKeys.status = 'error';
    session.gameState.dnaScreen.status = 'error';
    
    // Update vaccine key states to error (preserve success states)
    Object.keys(session.gameState.vaccineKeys.keyStates).forEach(keyId => {
      if (session.gameState.vaccineKeys.keyStates[keyId] !== 'success') {
        session.gameState.vaccineKeys.keyStates[keyId] = 'error';
      }
    });
    
    // Update DNA feedback ONLY if DNA didn't trigger the failure
    if (triggeringModule !== 'dna' && (!session.gameState.dnaScreen.feedback || !session.gameState.dnaScreen.feedback.correct)) {
      session.gameState.dnaScreen.feedback = {
        show: true,
        correct: false,
        points: 0, // 0 points if failed by another module
        message: 'Erreur dans un autre module.'
      };
    }
    
    // Broadcast the updated game state immediately
    this.broadcast({
      type: 'round_started', // Use round_started to update current state
      roundData: {
        sessionId: session.sessionId!,
        roundId: session.roundId!,
        gameState: session.gameState,
        roundComplete: false // Not complete yet, just propagating failure
      }
    });
  }

  private endRound(team: string, triggeringModule?: string): void {
    const session = this.teamSessions[team as keyof typeof this.teamSessions];
    session.roundComplete = true;

    // Calculate total points and update scoreboard (not for nemo)
    const totalPoints = this.calculateRoundPoints(session.moduleResults);
    if (team !== 'nemo') {
      this.updateScoreboard(team, totalPoints);
    }
    
    // Propagate failure to all modules if any failed
    const hasFailure = Object.values(session.moduleResults).some(result => result === false);
    if (hasFailure) {
      // Set all modules to error state
      session.gameState.vaccineKeys.status = 'error';
      session.gameState.dnaScreen.status = 'error';
      
      // Update key states to error if not already in success
      Object.keys(session.gameState.vaccineKeys.keyStates).forEach(keyId => {
        if (session.gameState.vaccineKeys.keyStates[keyId] !== 'success') {
          session.gameState.vaccineKeys.keyStates[keyId] = 'error';
        }
      });
      
      // Update DNA feedback ONLY if DNA didn't trigger the failure
      if (triggeringModule !== 'dna' && (!session.gameState.dnaScreen.feedback || !session.gameState.dnaScreen.feedback.correct)) {
        session.gameState.dnaScreen.feedback = {
          show: true,
          correct: false,
          points: 0, // 0 points if failed by another module
          message: 'Erreur dans un autre module.'
        };
      }
    }

    // Handle nemo 5-round progression BEFORE broadcasting
    if (team === 'nemo') {
      if (!session.currentRound) session.currentRound = 1;
      
      if (hasFailure) {
        // Reset to round 1 on failure
        session.currentRound = 1;
      } else {
        // Success - advance to next round first
        session.currentRound += 1;
        
        // Then check for victory
        if (session.currentRound > (session.maxRounds ?? 5)) {
          // Victory after completing all rounds!
          session.isVictory = true;
        }
      }
    }

    // Broadcast round ended with nemo info
    this.broadcast({
      type: 'round_ended',
      roundData: {
        sessionId: session.sessionId!,
        roundId: session.roundId!,
        gameState: session.gameState,
        roundComplete: true,
        team: team,
        nemoInfo: session.isNemo ? {
          currentRound: session.currentRound || 1,
          maxRounds: session.maxRounds || 5,
          isVictory: session.isVictory || false
        } : undefined
      }
    });

    // Schedule next round with server-controlled timing
    if (!session.nextRoundScheduled) {
      session.nextRoundScheduled = true;
      session.nextRoundTimeout = setTimeout(() => {
        if (session.active && !(team === 'nemo' && session.isVictory)) {
          this.startNewRound(team);
        }
      }, 4000);
    }
  }

  private async updateScoreboard(team: string, points: number): Promise<void> {
    try {
      const { Pool } = await import('pg');
      const pool = new Pool({
        connectionString: process.env.DATABASE_URL!,
      });

      // Map team names to scoreboard columns
      const teamColumn = team === 'americas' ? 'americas' : 
                        team === 'eurafrica' ? 'europe' : 'asia';

      // Get current scoreboard
      const currentResult = await pool.query(
        'SELECT * FROM scoreboard ORDER BY "updatedAt" DESC LIMIT 1'
      );

      const currentScores = currentResult.rows[0] || { americas: 0, europe: 0, asia: 0 };
      const newScore = Math.max(0, (currentScores[teamColumn] || 0) + points);
      
      // Update scoreboard
      const updatedAt = new Date().toISOString();
      await pool.query(
        `INSERT INTO scoreboard (americas, europe, asia, "updatedAt") VALUES ($1, $2, $3, $4)`,
        [
          teamColumn === 'americas' ? newScore : currentScores.americas,
          teamColumn === 'europe' ? newScore : currentScores.europe,
          teamColumn === 'asia' ? newScore : currentScores.asia,
          updatedAt
        ]
      );

      // Broadcast updated scoreboard
      this.broadcastScoreboardUpdate({
        americas: teamColumn === 'americas' ? newScore : currentScores.americas,
        europe: teamColumn === 'europe' ? newScore : currentScores.europe,
        asia: teamColumn === 'asia' ? newScore : currentScores.asia,
        updatedAt
      });

    } catch (error) {
      console.error('Error updating scoreboard:', error);
    }
  }
}

export const gameWsManager = new GameWebSocketManager();
export { GameWebSocketManager };

// Global initialization function for lazy initialization
export function ensureGameWebSocketInitialized() {
  gameWsManager.initialize();
}
