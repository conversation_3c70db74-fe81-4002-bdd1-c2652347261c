import { webSocketClient } from './websocket-client.js';

export class TeamSessionManager {
  constructor() {
    this.sessions = {
      americas: {
        id: null,
        active: false,
        roundId: null,
        gameState: {
          vaccineKeys: {
            keys: [],
            pressedSequence: [],
            keyStates: {},
            status: 'idle', // idle, active, success, error
          },
          dnaScreen: {
            sequence: [],
            status: 'idle', // idle, active, success, error
            feedback: null
          }
        },
        roundComplete: false
      },
      eurafrica: {
        id: null,
        active: false,
        roundId: null,
        gameState: {
          vaccineKeys: {
            keys: [],
            pressedSequence: [],
            keyStates: {},
            status: 'idle',
          },
          dnaScreen: {
            sequence: [],
            status: 'idle',
            feedback: null
          }
        },
        roundComplete: false
      },
      oceasia: {
        id: null,
        active: false,
        roundId: null,
        gameState: {
          vaccineKeys: {
            keys: [],
            pressedSequence: [],
            keyStates: {},
            status: 'idle',
          },
          dnaScreen: {
            sequence: [],
            status: 'idle',
            feedback: null
          }
        },
        roundComplete: false
      },
      nemo: {
        id: null,
        active: false,
        roundId: null,
        gameState: {
          vaccineKeys: {
            keys: [],
            pressedSequence: [],
            keyStates: {},
            status: 'idle',
          },
          dnaScreen: {
            sequence: [],
            status: 'idle',
            feedback: null
          }
        },
        roundComplete: false
      }
    };

    this.listeners = {
      onVaccineKeyUpdate: [],
      onDnaUpdate: [],
      onGameStateChange: [],
      onSessionStatusChange: [],
      onValidationResult: [],
      onRoundUpdate: []
    };

    // Buffer for events that arrive before session creation
    this.pendingEvents = [];

    this.setupWebSocketListeners();
  }

  // Get player's team from auth session
  async getCurrentPlayerTeam() {
    try {
      // Check if we're on nemo hostname first
      if (typeof window !== 'undefined' && window.location.hostname === 'nemo') {
        return 'nemo';
      }
      
      // In server-side context, we need to use the full URL
      const baseUrl = typeof window !== 'undefined' 
        ? window.location.origin 
        : (process.env.BETTER_AUTH_URL || 'http://localhost:4321');
      const response = await fetch(`${baseUrl}/api/auth/get-user`);
      if (response.ok) {
        const userData = await response.json();
        return userData.role || null;
      }
    } catch (error) {
      console.error('Failed to get user team:', error);
    }
    return null;
  }

  setupWebSocketListeners() {
    // Listen for session management messages
    webSocketClient.onSessionCreated(async (sessionData) => {
      const { team, sessionId } = sessionData;
      const currentTeam = await this.getCurrentPlayerTeam();
      
      // Only process session events for our team
      if (team === currentTeam && this.sessions[team]) {
        this.sessions[team].id = sessionId;
        this.sessions[team].active = true;
        this.notifySessionStatusChange();
        
        // Process any buffered events now that session is created
        await this.processPendingEvents(team);
        
        // Also notify team listeners if we have game state
        if (this.sessions[team].gameState) {
          this.notifyTeamListeners(team);
        }
      }
    });

    webSocketClient.onSessionClosed(async (sessionData) => {
      const { team } = sessionData;
      const currentTeam = await this.getCurrentPlayerTeam();
      
      // Only process session events for our team
      if (team === currentTeam && this.sessions[team]) {
        this.sessions[team].active = false;
        this.sessions[team].id = null;
        this.sessions[team].roundId = null;
        this.sessions[team].roundComplete = false;
        // Reset game state
        this.sessions[team].gameState.vaccineKeys.status = 'idle';
        this.sessions[team].gameState.vaccineKeys.keyStates = {};
        this.sessions[team].gameState.dnaScreen.status = 'idle';
        this.sessions[team].gameState.dnaScreen.feedback = null;
        this.notifyTeamListeners(team);
        this.notifySessionStatusChange();
      }
    });

    // Round-based listeners
    webSocketClient.onRoundStarted(async (roundData) => {
      const team = await this.getCurrentPlayerTeam();
      if (!team || !this.sessions[team]) return;
      
      const session = this.sessions[team];
      // Only update if this round belongs to our team's session
      if (session.id && roundData.sessionId === session.id) {
        this.processRoundStarted(team, roundData);
        // Notify listeners about round updates
        this.listeners.onRoundUpdate.forEach(callback => {
          try {
            callback(roundData);
          } catch (error) {
            console.error('Error in onRoundUpdate callback:', error);
          }
        });
      } else if (!session.id && this.sessionBelongsToTeam(roundData.sessionId, team)) {
        // Buffer the event if session not created yet but it's for our team
        this.pendingEvents.push({ type: 'round_started', data: roundData });
      }
    });

    webSocketClient.onRoundEnded(async (roundData) => {
      const team = await this.getCurrentPlayerTeam();
      if (!team || !this.sessions[team]) return;
      
      const session = this.sessions[team];
      // Only update if this round belongs to our team's session
      if (session.id && roundData.sessionId === session.id) {
        this.processRoundEnded(team, roundData);
        // Notify listeners about round updates
        this.listeners.onRoundUpdate.forEach(callback => {
          try {
            callback(roundData);
          } catch (error) {
            console.error('Error in onRoundUpdate callback:', error);
          }
        });
      } else if (!session.id && this.sessionBelongsToTeam(roundData.sessionId, team)) {
        // Buffer the event if session not created yet but it's for our team;
        this.pendingEvents.push({ type: 'round_ended', data: roundData });
      }
    });

    // Answer validation results
    webSocketClient.onAnswerValidation(async (result) => {
      const team = await this.getCurrentPlayerTeam();
      if (!team || !this.sessions[team]) return;
      
      const session = this.sessions[team];
      // Only process validation results for our team's session
      if (session.id && result.sessionId === session.id) {
        // If this is a DNA validation with feedback, update the DNA screen state immediately
        if (result.gameType === 'dna' && result.feedback) {
          session.gameState.dnaScreen.feedback = result.feedback;
          session.gameState.dnaScreen.status = result.succeeded ? 'success' : 'error';
          this.notifyTeamListeners(team);
        }
        
        // If this is a vaccine key validation with keyStates, update the key states immediately
        if (result.gameType === 'vaccine_key' && result.keyStates) {
          session.gameState.vaccineKeys.keyStates = result.keyStates;
          // Only update status for final results (when correctAnswer is provided or succeeded)
          if (result.succeeded || result.correctAnswer) {
            session.gameState.vaccineKeys.status = result.succeeded ? 'success' : 'error';
          }
          this.notifyTeamListeners(team);
        }
        
        // Handle validation feedback (points, correctAnswer, etc.)
        this.listeners.onValidationResult.forEach(callback => {
          try {
            callback(result);
          } catch (error) {
            console.error('Error in onValidationResult callback:', error);
          }
        });
      }
    });
  }

  // Helper to check if a sessionId belongs to a specific team
  sessionBelongsToTeam(sessionId, team) {
    // Session IDs follow pattern: {team}-{timestamp}
    return sessionId && sessionId.startsWith(`${team}-`);
  }

  // Process round started event
  processRoundStarted(team, roundData) {
    const session = this.sessions[team];
    session.roundId = roundData.roundId;
    session.gameState = roundData.gameState;
    session.roundComplete = roundData.roundComplete;
    this.notifyTeamListeners(team);
  }

  // Process round ended event
  processRoundEnded(team, roundData) {
    const session = this.sessions[team];
    session.roundId = roundData.roundId;
    session.gameState = roundData.gameState;
    session.roundComplete = roundData.roundComplete;
    this.notifyTeamListeners(team);
  }

  // Process pending events after session creation
  async processPendingEvents(team) {
    const session = this.sessions[team];
    if (!session.id) return;
    
    // Filter events for this team's session
    const teamEvents = this.pendingEvents.filter(event => 
      event.data.sessionId === session.id
    );

    // Process each event
    for (const event of teamEvents) {      
      if (event.type === 'round_started') {
        this.processRoundStarted(team, event.data);
      } else if (event.type === 'round_ended') {
        this.processRoundEnded(team, event.data);
      }
    }

    // Remove processed events
    this.pendingEvents = this.pendingEvents.filter(event => 
      event.data.sessionId !== session.id
    );
  }

  // Join session based on player's team
  async joinSession() {
    const team = await this.getCurrentPlayerTeam();
    
    if (!team || !this.sessions[team] || !this.sessions[team].active) {
      return false;
    }

    return true;
  }

  // Public methods for components to use
  async submitVaccineKeyAnswer(answer) {
    try {
      const team = await this.getCurrentPlayerTeam();
      if (!team || !this.sessions[team]?.active) {
        console.warn('Cannot submit vaccine key answer: No active session for team', team);
        return false;
      }
      
      const session = this.sessions[team];
      if (session.gameState.vaccineKeys.status !== 'active' || !session.id) {
        console.warn('Cannot submit vaccine key answer: Game not active or no session ID');
        return false;
      }
      
      webSocketClient.submitVaccineKeyAnswer(team, session.id, answer);
      return true;
    } catch (error) {
      console.error('Error submitting vaccine key answer:', error);
      return false;
    }
  }

  async submitDnaAnswer(answer) {
    try {
      const team = await this.getCurrentPlayerTeam();
      if (!team || !this.sessions[team]?.active) {
        console.warn('Cannot submit DNA answer: No active session for team', team);
        return false;
      }
      
      const session = this.sessions[team];
      if (session.gameState.dnaScreen.status !== 'active' || !session.id) {
        console.warn('Cannot submit DNA answer: Game not active or no session ID');
        return false;
      }
      
      webSocketClient.submitDnaAnswer(team, session.id, answer);
      return true;
    } catch (error) {
      console.error('Error submitting DNA answer:', error);
      return false;
    }
  }

  // Listener management
  onVaccineKeyUpdate(callback) {
    if (!this.listeners.onVaccineKeyUpdate.includes(callback)) {
      this.listeners.onVaccineKeyUpdate.push(callback);
      
      // Immediately notify the new listener of current state if we have an active session
      this.notifyNewVaccineKeyListener(callback);
    }
    // Return unsubscribe function
    return () => {
      const index = this.listeners.onVaccineKeyUpdate.indexOf(callback);
      if (index > -1) this.listeners.onVaccineKeyUpdate.splice(index, 1);
    };
  }

  onDnaUpdate(callback) {
    if (!this.listeners.onDnaUpdate.includes(callback)) {
      this.listeners.onDnaUpdate.push(callback);
    }
    return () => {
      const index = this.listeners.onDnaUpdate.indexOf(callback);
      if (index > -1) this.listeners.onDnaUpdate.splice(index, 1);
    };
  }

  onGameStateChange(callback) {
    if (!this.listeners.onGameStateChange.includes(callback)) {
      this.listeners.onGameStateChange.push(callback);
      
      // Immediately notify the new listener of current state if we have an active session
      this.notifyNewGameStateListener(callback);
    }
    return () => {
      const index = this.listeners.onGameStateChange.indexOf(callback);
      if (index > -1) this.listeners.onGameStateChange.splice(index, 1);
    };
  }

  onSessionStatusChange(callback) {
    if (!this.listeners.onSessionStatusChange.includes(callback)) {
      this.listeners.onSessionStatusChange.push(callback);
    }
    return () => {
      const index = this.listeners.onSessionStatusChange.indexOf(callback);
      if (index > -1) this.listeners.onSessionStatusChange.splice(index, 1);
    };
  }

  onValidationResult(callback) {
    if (!this.listeners.onValidationResult.includes(callback)) {
      this.listeners.onValidationResult.push(callback);
    }
    return () => {
      const index = this.listeners.onValidationResult.indexOf(callback);
      if (index > -1) this.listeners.onValidationResult.splice(index, 1);
    };
  }

  onRoundUpdate(callback) {
    if (!this.listeners.onRoundUpdate.includes(callback)) {
      this.listeners.onRoundUpdate.push(callback);
    }
    return () => {
      const index = this.listeners.onRoundUpdate.indexOf(callback);
      if (index > -1) this.listeners.onRoundUpdate.splice(index, 1);
    };
  }

  // Helper methods to notify new listeners of current state
  async notifyNewVaccineKeyListener(callback) {
    const team = await this.getCurrentPlayerTeam();
    if (team && this.sessions[team] && this.sessions[team].active && this.sessions[team].gameState) {
      try {
        callback(this.sessions[team].gameState.vaccineKeys);
      } catch (error) {
        console.error('Error notifying new vaccine key listener:', error);
      }
    }
  }

  async notifyNewGameStateListener(callback) {
    const team = await this.getCurrentPlayerTeam();
    if (team && this.sessions[team] && this.sessions[team].active && this.sessions[team].gameState) {
      try {
        callback({
          vaccineKeys: this.sessions[team].gameState.vaccineKeys,
          dnaScreen: this.sessions[team].gameState.dnaScreen,
          sessionActive: this.sessions[team].active
        });
      } catch (error) {
        console.error('Error notifying new game state listener:', error);
      }
    }
  }

  notifyTeamListeners(team) {
    if (!this.sessions[team]) return;
    
    const session = this.sessions[team];
    
    this.listeners.onVaccineKeyUpdate.forEach(callback => {
      try {
        callback(session.gameState.vaccineKeys);
      } catch (error) {
        console.error('Error in onVaccineKeyUpdate callback:', error);
      }
    });
    
    this.listeners.onDnaUpdate.forEach(callback => {
      try {
        callback(session.gameState.dnaScreen);
      } catch (error) {
        console.error('Error in onDnaUpdate callback:', error);
      }
    });
    
    this.listeners.onGameStateChange.forEach(callback => {
      try {
        callback({
          vaccineKeys: session.gameState.vaccineKeys,
          dnaScreen: session.gameState.dnaScreen,
          sessionActive: session.active
        });
      } catch (error) {
        console.error('Error in onGameStateChange callback:', error);
      }
    });
  }

  notifySessionStatusChange() {
    if (this.listeners.onSessionStatusChange.length > 0) {
      const sessionStatus = {};
      Object.keys(this.sessions).forEach(team => {
        sessionStatus[team] = {
          active: this.sessions[team].active
        };
      });
      this.listeners.onSessionStatusChange.forEach(callback => {
        try {
          callback(sessionStatus);
        } catch (error) {
          console.error('Error in onSessionStatusChange callback:', error);
        }
      });
    }
  }

  // Initialize the session manager
  async initialize() {
    await webSocketClient.ensureConnection();
    
    // Try to join session for current player's team
    await this.joinSession();
  }

  async getCurrentSessionId() {
    const team = await this.getCurrentPlayerTeam();
    if (!team || !this.sessions[team]) {
      return null;
    }
    
    const session = this.sessions[team];
    return session.active ? session.id : null;
  }

  async getCurrentTeamGameState() {
    const team = await this.getCurrentPlayerTeam();
    if (!team || !this.sessions[team]) {
      return {
        vaccineKeys: { status: 'idle' },
        dnaScreen: { status: 'idle' },
        sessionActive: false,
        roundComplete: false
      };
    }
    
    const session = this.sessions[team];
    return {
      vaccineKeys: session.gameState.vaccineKeys,
      dnaScreen: session.gameState.dnaScreen,
      sessionActive: session.active,
      roundComplete: session.roundComplete
    };
  }
}

// Singleton instance
export const teamSessionManager = new TeamSessionManager();
