import { useState, useRef, useEffect } from 'react';
import KeypadGrid from './KeypadGrid.jsx';

export default function SecurityKeypad() {
    const secretPattern = [750, 750, 350, 350];
    const tolerance = 50;
    
    const [currentPattern, setCurrentPattern] = useState([]);
    const [lastPressTime, setLastPressTime] = useState(0);
    const [display, setDisplay] = useState('');
    const [isUnlocked, setIsUnlocked] = useState(false);
    const [isLocked, setIsLocked] = useState(false);
    const [keyPressCount, setKeyPressCount] = useState(0);
    
    const audioRefs = useRef([]);
    
    useEffect(() => {
        // Initialize audio objects only on client side
        if (typeof window !== 'undefined') {
            audioRefs.current = [
                new Audio('/vault-sound.mp3'),
                new Audio('/vault-sound.mp3'),
                new Audio('/vault-sound.mp3'),
                new Audio('/vault-sound.mp3'),
                new Audio('/vault-sound.mp3')
            ];
            
            // Preload all audio files
            audioRefs.current.forEach(audio => {
                audio.preload = 'auto';
                audio.load();
            });
        }
    }, []);

    const checkPattern = (intervals) => {
        if (intervals.length !== 4) return false;
        
        let matches = 0;
        for (let i = 0; i < 4; i++) {
            const expected = secretPattern[i];
            const actual = intervals[i];
            
            if (Math.abs(actual - expected) <= tolerance) {
                matches++;
            }
        }
        
        return matches >= 3;
    };

    const playAudio = (index) => {
        try {
            const audio = audioRefs.current[index];
            if (audio) {
                audio.currentTime = 0;
                audio.play().catch(err => console.log('Audio play failed:', err));
            }
        } catch (err) {
            console.log('Audio error:', err);
        }
    };

    const handleKeyPress = () => {
        const currentTime = Date.now();
        
        // Prevent input when locked or unlocked
        if (isLocked || isUnlocked) {
            return;
        }
        
        // Debounce - ignore presses within 50ms of each other
        if (lastPressTime !== 0 && currentTime - lastPressTime < 50) {
            return;
        }
        
        if (lastPressTime === 0) {
            // First press
            setLastPressTime(currentTime);
            setDisplay('●');
            setCurrentPattern([]);
            setKeyPressCount(1);
            playAudio(0);
            return;
        }
        
        const timeDiff = currentTime - lastPressTime;
        const newPattern = [...currentPattern, timeDiff];
        const newCount = keyPressCount + 1;
        
        setCurrentPattern(newPattern);
        setLastPressTime(currentTime);
        setDisplay(prev => prev + '●');
        setKeyPressCount(newCount);
        
        // Play corresponding audio (0-indexed)
        playAudio(newCount - 1);
        
        // Check if we have 4 intervals (5 presses total)
        if (newPattern.length === 4) {
            if (checkPattern(newPattern)) {
                setDisplay('UNLOCKED');
                setIsUnlocked(true);
                
                // Request vault access token and redirect
                setTimeout(async () => {
                    try {
                        const response = await fetch('/api/machine/request-access', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ machineType: 'vault' })
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            // Redirect with vault access token
                            window.location.href = `/info?token=${result.token}`;
                        } else {
                            console.error('Failed to get vault token:', result.message);
                            // Fallback redirect without token
                            window.location.href = '/info';
                        }
                    } catch (error) {
                        console.error('Error requesting vault token:', error);
                        // Fallback redirect without token
                        window.location.href = '/info';
                    }
                }, 1500);
            } else {
                // Lock keypad and show incorrect password message
                setIsLocked(true);
                setDisplay('Incorrect Password');
                setTimeout(() => {
                    setCurrentPattern([]);
                    setDisplay('');
                    setLastPressTime(0);
                    setIsLocked(false);
                    setKeyPressCount(0);
                }, 2000);
            }
        }
    };

    return (
        <div className="security-keypad">
            <div className="keypad-container">
                <div className={`input-display ${display === 'Incorrect Password' ? 'error' : ''}`}>
                    {display}
                </div>
                
                <KeypadGrid 
                    onKeyPress={handleKeyPress} 
                    disabled={isUnlocked || isLocked}
                />
            </div>
    </div>
    );
}
