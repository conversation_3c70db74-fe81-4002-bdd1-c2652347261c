import React, { useState, useEffect } from 'react';
import { teamSessionManager } from '../lib/team-session-manager.js';

export default function VictoryBanner() {
  const [isVictoryActive, setIsVictoryActive] = useState(false);
  const [isFlashing, setIsFlashing] = useState(false);
  const [nemoInfo, setNemoInfo] = useState({
    currentRound: 1,
    maxRounds: 5,
    isVictory: false,
    isNemoMode: false,
    machineOccupied: false
  });

  useEffect(() => {
    let isMounted = true;
    let unsubscribeRoundUpdate = null;
    let occupancyInterval = null;

    const initializeNemoTracking = async () => {
      try {
        // Check if we're on nemo hostname BUT NOT on the machine page (i.e., on dashboard)
        const isOnNemoHostname = typeof window !== 'undefined' && window.location.hostname === 'nemo';
        const isOnDashboard = typeof window !== 'undefined' && window.location.pathname === '/map';
        
        const shouldTrackNemo = isOnNemoHostname && isOnDashboard;
        
        if (shouldTrackNemo) {
          setNemoInfo(prev => ({ ...prev, isNemoMode: true }));

          // Set up listener for round updates to track nemo progress
          unsubscribeRoundUpdate = teamSessionManager.onRoundUpdate((roundData) => {
            if (!isMounted) return;
            
            // Check if this is nemo and has round info
            if (roundData && roundData.nemoInfo) {
              setNemoInfo(prev => ({
                currentRound: roundData.nemoInfo.currentRound,
                maxRounds: roundData.nemoInfo.maxRounds,
                isVictory: roundData.nemoInfo.isVictory,
                isNemoMode: true,
                machineOccupied: prev.machineOccupied
              }));
              
              // Show victory banner when victory is achieved
              if (roundData.nemoInfo.isVictory) {
                setIsVictoryActive(true);
              }
            }
          });

          // Poll machine occupancy to see if someone is using the nemo machine
          const checkMachineOccupancy = async () => {
            try {
              const response = await fetch('/api/machine/occupancy');
              if (response.ok) {
                const data = await response.json();
                if (data.success) {
                  setNemoInfo(prev => ({
                    ...prev,
                    machineOccupied: data.occupancy.machine
                  }));
                }
              }
            } catch (error) {
              console.error('Error checking machine occupancy:', error);
            }
          };

          // Check immediately and then every 1000 milliseconds
          checkMachineOccupancy();
          occupancyInterval = setInterval(checkMachineOccupancy, 1000);

          // Initialize the team session manager
          await teamSessionManager.initialize();
        }
      } catch (error) {
        console.error("Nemo tracking setup failed:", error);
      }
    };

    initializeNemoTracking();

    return () => {
      isMounted = false;
      if (unsubscribeRoundUpdate) unsubscribeRoundUpdate();
      if (occupancyInterval) clearInterval(occupancyInterval);
    };
  }, []);

  useEffect(() => {
    if (isVictoryActive) {
      const flashInterval = setInterval(() => {
        setIsFlashing(prev => !prev);
      }, 500); // Flash every 500ms like AlarmBanner

      return () => clearInterval(flashInterval);
    } else {
      setIsFlashing(false);
    }
  }, [isVictoryActive]);

  // Show progress only when machine is occupied, but victory always when achieved
  if (!nemoInfo.isNemoMode) return null;

  const showProgress = nemoInfo.machineOccupied && !isVictoryActive;
  const showVictory = isVictoryActive && nemoInfo.isVictory;

  // Show either progress or victory, both using the same full-screen AlarmBanner style
  if (!showProgress && !showVictory) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 flex items-end justify-center pb-16 pointer-events-none transition-all duration-200 ${
        isFlashing ? 'bg-green-600' : 'bg-green-700'
      }`}
      style={{ backgroundColor: isFlashing ? 'rgba(34, 197, 94, 0.4)' : 'rgba(21, 128, 61, 0.4)' }}
    >
      <div 
        className={`text-white text-6xl font-bold text-center px-8 py-4 rounded-lg shadow-2xl border-4 border-green-700 transition-all duration-200 ${
          isFlashing ? 'bg-green-600' : 'bg-green-700'
        }`}
        style={{
          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
          animation: isFlashing ? 'pulse 0.5s ease-in-out infinite' : 'none'
        }}
      >
        {showVictory ? (
          <>
            <div>🎉 VICTOIRE ! 🎉</div>
            <div className="text-2xl font-semibold mt-2 text-green-200">
              Vaccin synthétisé avec succès !
            </div>
            <div className="text-lg mt-1 text-yellow-300">
              La Terre entière vous remercie !
            </div>
          </>
        ) : (
          <>
            <div>{
              nemoInfo.isVictory 
                ? "Vaccin synthétisé avec succès !"
                : "Tentative de synthétisation du vaccin"
            }
            </div>
            <div className="text-2xl font-semibold mt-2 text-green-200">
              {nemoInfo.isVictory 
                ? "La Terre entière vous remercie !"
                : `Essai ${nemoInfo.currentRound}/${nemoInfo.maxRounds}`
              }
            </div>
          </>
        )}
      </div>
      <style jsx="true">{`
        @keyframes pulse {
          from {
            transform: scale(1);
          }
          to {
            transform: scale(1.05);
          }
        }
      `}</style>
    </div>
  );
}
