import { config } from "dotenv";
config();

import type { APIRoute } from 'astro';
import { machineSessionManager } from '../../../lib/machine-session-manager';

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const body = await request.json();
    const { token, machineType } = body;

    if (!token || !machineType) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Token ou type de machine manquant'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // For vault tokens, skip session check
    if (machineType !== 'vault') {
      const session = locals.session;

      if (!session) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Non authentifié'
        }), {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Consume (validate and remove) the access token
    const tokenData = machineSessionManager.consumeAccessToken(token);

    if (!tokenData) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Token invalide ou expiré'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify machine type matches token
    if (tokenData.machineType !== machineType) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Type de machine incorrect'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Token valide',
      sessionId: tokenData.sessionId,
      machineType: tokenData.machineType
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error validating token:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Erreur serveur'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
