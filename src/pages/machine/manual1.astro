---
import Layout from '../../layouts/Layout.astro';
import { checkAccess, extractHostname, getAccessDeniedMessage } from '../../lib/access-control';
import { auth } from '../../lib/auth';

// Check authentication
const session = Astro.locals.session;
if (!session) {
  const redirectUrl = encodeURIComponent('/machine/manual1');
  return Astro.redirect(`/login?redirect=${redirectUrl}`);
}

// Get user data with role for hostname access control
const authSession = await auth.api.getSession({
  headers: Astro.request.headers,
});

if (!authSession?.user) {
  return Astro.redirect(`/login?redirect=${encodeURIComponent('/machine/manual1')}`);
}

// Check hostname-based access control
const hostname = extractHostname(Astro.request);
const accessCheck = checkAccess(authSession.user.role, hostname);
const isNemoHostname = hostname === 'nemo';

if (!accessCheck.authorized) {
  // Create an error page or redirect with error message
  const errorMessage = getAccessDeniedMessage(authSession.user.role, accessCheck.location || hostname);
  const errorUrl = `/access-denied?message=${encodeURIComponent(errorMessage)}`;
  return Astro.redirect(errorUrl);
}

// Check for access token
const url = new URL(Astro.request.url);
const token = url.searchParams.get('token');

if (!token) {
  // No token, redirect to options page
  return Astro.redirect('/machine/options');
}

// Validate token on server side
try {
  const response = await fetch(`${url.origin}/api/machine/validate-token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...Object.fromEntries(Astro.request.headers)
    },
    body: JSON.stringify({ token, machineType: 'manual1' })
  });

  if (!response.ok) {
    // Invalid token, redirect to options
    return Astro.redirect('/machine/options');
  }
} catch (error) {
  console.error('Token validation error:', error);
  return Astro.redirect('/machine/options');
}
---

<Layout title="Manuel 1 - Procédures Standard">
  <div class="h-full bg-gradient-to-br from-slate-900 via-green-900 to-slate-800 py-4 px-4 relative">
    {/* Back to Options Button */}
    <div class="absolute top-4 right-4 z-50">
      <a 
        href="/machine/options" 
        class="flex items-center gap-2 px-3 py-2 md:px-4 md:py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm shadow-lg"
      >
        <span>←</span>
        <span class="hidden sm:inline">Options</span>
      </a>
    </div>

    <div class="max-w-4xl w-full h-full overflow-y-scroll mx-auto text-center">
      <h1 class="text-2xl md:text-4xl font-bold text-white mb-2 md:mb-4">
        📋 Manuel 1
      </h1>
      <h2 class="text-lg md:text-xl text-green-300 mb-4 md:mb-6 px-2">
        Procédures Manuelles Standard
      </h2>
      
      <div class="bg-black/30 backdrop-blur-sm rounded-lg p-3 md:p-6 border border-green-500/20 mb-4">
        <div class="text-white text-base md:text-lg mb-4 md:mb-6">
          Manuel des Symboles - Procédures de Séquençage
        </div>
        
        <div class="bg-green-900/30 p-3 md:p-4 rounded-lg mb-4 text-left">
          <h3 class="text-green-300 font-bold text-sm md:text-lg mb-2 md:mb-3">📋 Instructions</h3>
          <div class="text-gray-300 text-xs md:text-sm space-y-2">
            <p>La machine a des touches accompagnées de symboles. Tous les symboles sur la machine appartiennent à une même colonne. Trouvez la colonne et listez les symboles de haut en bas.</p>
            <p>L'opérateur de la machine doit appuyer sur la touche correspondante au symbole indiqué dans la colonne si le symbole est présent sur la machine. Si le symbole n'est pas présent, l'opérateur peut ignorer le symbole et passer au symbole suivant.</p>
          </div>
        </div>

        <div class="bg-green-900/30 p-3 md:p-4 rounded-lg mb-4 text-left">
          <h3 class="text-green-300 font-bold text-sm md:text-lg mb-2 md:mb-3">💡 Exemple</h3>
          <div class="text-gray-300 text-xs md:text-sm space-y-1 md:space-y-2">
            <p><strong>Avec ⫸ ⨓ ⨋ sur la machine.</strong></p>
            <p>Seule la colonne 6 contient tous les symboles de la machine.</p>
            <p>La séquence est donc ▥ ᚠ ఞ ⨓ ☙ ⨋.</p>
            <p><strong>L'opérateur appuie séquentiellement sur les touches ⨓ ⫸ ⨋.</strong></p>
          </div>
        </div>

{isNemoHostname ? (
        <div class="bg-green-900/30 p-3 md:p-4 rounded-lg text-left">
          <h3 class="text-green-300 font-bold text-sm md:text-lg mb-2 md:mb-3">📊 Tableaux des Symboles - Configurations</h3>
          
          {/* Tab Navigation */}
          <div class="flex space-x-2 mb-4">
            <button id="tab-eb" class="tab-button active px-3 py-1 rounded text-xs font-bold bg-green-600 text-white">E.B</button>
            <button id="tab-ps" class="tab-button px-3 py-1 rounded text-xs font-bold bg-green-800 text-gray-300 hover:bg-green-700">P.S</button>
            <button id="tab-vt" class="tab-button px-3 py-1 rounded text-xs font-bold bg-green-800 text-gray-300 hover:bg-green-700">V.T</button>
          </div>

          {/* E.B Configuration */}
          <div id="config-eb" class="config-content">
            <h4 class="text-green-400 font-semibold text-xs mb-2">Configuration E.B</h4>
            <div class="overflow-x-auto">
              <table class="w-full text-center text-gray-300 min-w-max">
                <thead>
                  <tr class="border-b border-green-500/30">
                    <th class="px-2 py-1 text-green-300 text-xs">1</th>
                    <th class="px-2 py-1 text-green-300 text-xs">2</th>
                    <th class="px-2 py-1 text-green-300 text-xs">3</th>
                    <th class="px-2 py-1 text-green-300 text-xs">4</th>
                    <th class="px-2 py-1 text-green-300 text-xs">5</th>
                    <th class="px-2 py-1 text-green-300 text-xs">6</th>
                  </tr>
                </thead>
                <tbody class="font-mono text-sm">
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">◓</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◔</td>
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◙</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◒</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">◔</td>
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◙</td>
                    <td class="px-2 py-1">◘</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">◔</td>
                    <td class="px-2 py-1">◕</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">●</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◙</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◗</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* P.S Configuration */}
          <div id="config-ps" class="config-content hidden">
            <h4 class="text-green-400 font-semibold text-xs mb-2">Configuration P.S</h4>
            <div class="overflow-x-auto">
              <table class="w-full text-center text-gray-300 min-w-max">
                <thead>
                  <tr class="border-b border-green-500/30">
                    <th class="px-2 py-1 text-green-300 text-xs">1</th>
                    <th class="px-2 py-1 text-green-300 text-xs">2</th>
                    <th class="px-2 py-1 text-green-300 text-xs">3</th>
                    <th class="px-2 py-1 text-green-300 text-xs">4</th>
                    <th class="px-2 py-1 text-green-300 text-xs">5</th>
                    <th class="px-2 py-1 text-green-300 text-xs">6</th>
                  </tr>
                </thead>
                <tbody class="font-mono text-sm">
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">◒</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">◙</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">◔</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">◒</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◔</td>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">◙</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◗</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">◔</td>
                    <td class="px-2 py-1">○</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">○</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◙</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* V.T Configuration */}
          <div id="config-vt" class="config-content hidden">
            <h4 class="text-green-400 font-semibold text-xs mb-2">Configuration V.T</h4>
            <div class="overflow-x-auto">
              <table class="w-full text-center text-gray-300 min-w-max">
                <thead>
                  <tr class="border-b border-green-500/30">
                    <th class="px-2 py-1 text-green-300 text-xs">1</th>
                    <th class="px-2 py-1 text-green-300 text-xs">2</th>
                    <th class="px-2 py-1 text-green-300 text-xs">3</th>
                    <th class="px-2 py-1 text-green-300 text-xs">4</th>
                    <th class="px-2 py-1 text-green-300 text-xs">5</th>
                    <th class="px-2 py-1 text-green-300 text-xs">6</th>
                  </tr>
                </thead>
                <tbody class="font-mono text-sm">
                   <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">◒</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">◙</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◔</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">◑</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">●</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◔</td>
                    <td class="px-2 py-1">◙</td>
                    <td class="px-2 py-1">◗</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">◕</td>
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">◔</td>
                    <td class="px-2 py-1">◐</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">◑</td>
                  </tr>
                  <tr class="border-b border-green-500/20">
                    <td class="px-2 py-1">●</td>
                    <td class="px-2 py-1">◗</td>
                    <td class="px-2 py-1">○</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">◖</td>
                  </tr>
                  <tr>
                    <td class="px-2 py-1">◘</td>
                    <td class="px-2 py-1">◖</td>
                    <td class="px-2 py-1">◙</td>
                    <td class="px-2 py-1">◓</td>
                    <td class="px-2 py-1">◒</td>
                    <td class="px-2 py-1">◗</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        ) : (
        <div class="bg-green-900/30 p-3 md:p-4 rounded-lg text-left">
          <h3 class="text-green-300 font-bold text-sm md:text-lg mb-2 md:mb-3">📊 Tableau des Symboles</h3>
          <div class="overflow-x-auto">
            <table class="w-full text-center text-gray-300 min-w-max">
              <thead>
                <tr class="border-b border-green-500/30">
                  <th class="px-2 py-1 text-green-300 text-xs">1</th>
                  <th class="px-2 py-1 text-green-300 text-xs">2</th>
                  <th class="px-2 py-1 text-green-300 text-xs">3</th>
                  <th class="px-2 py-1 text-green-300 text-xs">4</th>
                  <th class="px-2 py-1 text-green-300 text-xs">5</th>
                  <th class="px-2 py-1 text-green-300 text-xs">6</th>
                </tr>
              </thead>
              <tbody class="font-mono text-sm">
                <tr class="border-b border-green-500/20">
                  <td class="px-2 py-1">⍨</td>
                  <td class="px-2 py-1">✺</td>
                  <td class="px-2 py-1">⩐</td>
                  <td class="px-2 py-1">⩩</td>
                  <td class="px-2 py-1">℘</td>
                  <td class="px-2 py-1">▥</td>
                </tr>
                <tr class="border-b border-green-500/20">
                  <td class="px-2 py-1">⏧</td>
                  <td class="px-2 py-1">▥</td>
                  <td class="px-2 py-1">⌬</td>
                  <td class="px-2 py-1">⫸</td>
                  <td class="px-2 py-1">⚚</td>
                  <td class="px-2 py-1">ᚠ</td>
                </tr>
                <tr class="border-b border-green-500/20">
                  <td class="px-2 py-1">✹</td>
                  <td class="px-2 py-1">ᚨ</td>
                  <td class="px-2 py-1">༴</td>
                  <td class="px-2 py-1">✺</td>
                  <td class="px-2 py-1">ఠ</td>
                  <td class="px-2 py-1">ఞ</td>
                </tr>
                <tr class="border-b border-green-500/20">
                  <td class="px-2 py-1">⸎</td>
                  <td class="px-2 py-1">▤</td>
                  <td class="px-2 py-1">⦞</td>
                  <td class="px-2 py-1">⫁</td>
                  <td class="px-2 py-1">⌭</td>
                  <td class="px-2 py-1">⨓</td>
                </tr>
                <tr class="border-b border-green-500/20">
                  <td class="px-2 py-1">⨋</td>
                  <td class="px-2 py-1">℘</td>
                  <td class="px-2 py-1">▤</td>
                  <td class="px-2 py-1">⭈</td>
                  <td class="px-2 py-1">⍨</td>
                  <td class="px-2 py-1">⫸</td>
                </tr>
                <tr class="border-b border-green-500/20">
                  <td class="px-2 py-1">⫷</td>
                  <td class="px-2 py-1">⟪</td>
                  <td class="px-2 py-1">❋</td>
                  <td class="px-2 py-1">⍼</td>
                  <td class="px-2 py-1">༴</td>
                  <td class="px-2 py-1">☙</td>
                </tr>
                <tr>
                  <td class="px-2 py-1">☙</td>
                  <td class="px-2 py-1">໒</td>
                  <td class="px-2 py-1">⫁</td>
                  <td class="px-2 py-1">⧔</td>
                  <td class="px-2 py-1">⩪</td>
                  <td class="px-2 py-1">⨋</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        )}
      </div>
    </div>
  </div>
  
  <script>
    // Release machine when user leaves the page
    function releaseMachine() {
      navigator.sendBeacon('/api/machine/release', JSON.stringify({
        machineType: 'manual1'
      }));
    }

    // Handle page unload
    window.addEventListener('beforeunload', releaseMachine);
    window.addEventListener('pagehide', releaseMachine);
    
    // Handle navigation away (for SPAs)
    window.addEventListener('popstate', releaseMachine);
    
    // Tab functionality for nemo hostname
    if (window.location.hostname === 'nemo') {
      document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const configContents = document.querySelectorAll('.config-content');
        
        tabButtons.forEach(button => {
          button.addEventListener('click', function(event) {
            const target = event.currentTarget as HTMLButtonElement;
            const targetId = target.id.replace('tab-', 'config-');
            
            // Remove active class from all tabs
            tabButtons.forEach(btn => {
              btn.classList.remove('active', 'bg-green-600', 'text-white');
              btn.classList.add('bg-green-800', 'text-gray-300');
            });
            
            // Add active class to clicked tab
            target.classList.add('active', 'bg-green-600', 'text-white');
            target.classList.remove('bg-green-800', 'text-gray-300');
            
            // Hide all config contents
            configContents.forEach(content => content.classList.add('hidden'));
            
            // Show target config content
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
              targetElement.classList.remove('hidden');
            }
          });
        });
      });
    }
  </script>
</Layout>
