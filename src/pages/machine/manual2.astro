---
import Layout from '../../layouts/Layout.astro';
import { checkAccess, extractHostname, getAccessDeniedMessage } from '../../lib/access-control';
import { auth } from '../../lib/auth';

// Check authentication
const session = Astro.locals.session;
if (!session) {
  const redirectUrl = encodeURIComponent('/machine/manual2');
  return Astro.redirect(`/login?redirect=${redirectUrl}`);
}

// Get user data with role for hostname access control
const authSession = await auth.api.getSession({
  headers: Astro.request.headers,
});

if (!authSession?.user) {
  return Astro.redirect(`/login?redirect=${encodeURIComponent('/machine/manual2')}`);
}

// Check hostname-based access control
const hostname = extractHostname(Astro.request);
const accessCheck = checkAccess(authSession.user.role, hostname);
const isNemoHostname = hostname === 'nemo';

if (!accessCheck.authorized) {
  // Create an error page or redirect with error message
  const errorMessage = getAccessDeniedMessage(authSession.user.role, accessCheck.location || hostname);
  const errorUrl = `/access-denied?message=${encodeURIComponent(errorMessage)}`;
  return Astro.redirect(errorUrl);
}

// Check for access token
const url = new URL(Astro.request.url);
const token = url.searchParams.get('token');

if (!token) {
  // No token, redirect to options page
  return Astro.redirect('/machine/options');
}

// Validate token on server side
try {
  const response = await fetch(`${url.origin}/api/machine/validate-token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...Object.fromEntries(Astro.request.headers)
    },
    body: JSON.stringify({ token, machineType: 'manual2' })
  });

  if (!response.ok) {
    // Invalid token, redirect to options
    return Astro.redirect('/machine/options');
  }
} catch (error) {
  console.error('Token validation error:', error);
  return Astro.redirect('/machine/options');
}
---

<Layout title="Manuel 2 - Analyses de Laboratoire">
  <div class="h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 py-4 px-4 relative">
    {/* Back to Options Button */}
    <div class="absolute top-4 right-4 z-50">
      <a 
        href="/machine/options" 
        class="flex items-center gap-2 px-3 py-2 md:px-4 md:py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm shadow-lg"
      >
        <span>←</span>
        <span class="hidden sm:inline">Options</span>
      </a>
    </div>

    <div class="max-w-4xl w-full h-full overflow-y-scroll mx-auto text-center">
      <h1 class="text-2xl md:text-4xl font-bold text-white mb-2 md:mb-4">
        🔬 Manuel 2
      </h1>
      <h2 class="text-lg md:text-xl text-purple-300 mb-4 md:mb-6 px-2">
        Analyses de Laboratoire Avancées
      </h2>
      
      <div class="bg-black/30 backdrop-blur-sm rounded-lg p-3 md:p-6 border border-purple-500/20 mb-4">
        <div class="text-white text-base md:text-lg mb-4 md:mb-6">
          Manuel ADN - Règles de Réplication des Nucléotides
        </div>
        
        <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg mb-3 text-left">
          <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2 md:mb-3">🔬 Règle Générale</h3>
          <div class="text-gray-300 text-xs md:text-sm">
            <p><strong>Sauf mention contraire explicite, le '?' est exclu dans la séquence de nucléotides pour déterminer la position à répliquer.</strong></p>
          </div>
        </div>

        <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg mb-3 text-left">
          <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2 md:mb-3">💡 Exemple</h3>
          <div class="text-gray-300 text-xs md:text-sm space-y-1">
            <p><strong>Avec C ? T C</strong>, le premier nucléotide est C, le second est T, le troisième est C.</p>
            <p>Au total il y a 4 nucléotides incluant le '?'.</p>
            <p>Il n'y a pas de A, donc on réplique le second nucléotide, qui est <strong>T</strong>.</p>
          </div>
        </div>

{isNemoHostname ? (
        <div>
          {/* Tab Navigation */}
          <div class="flex space-x-2 mb-4">
            <button id="dna-tab-eb" class="dna-tab-button active px-3 py-1 rounded text-xs font-bold bg-purple-600 text-white">E.B</button>
            <button id="dna-tab-ps" class="dna-tab-button px-3 py-1 rounded text-xs font-bold bg-purple-800 text-gray-300 hover:bg-purple-700">P.S</button>
            <button id="dna-tab-vt" class="dna-tab-button px-3 py-1 rounded text-xs font-bold bg-purple-800 text-gray-300 hover:bg-purple-700">V.T</button>
          </div>

          {/* E.B Configuration */}
          <div id="dna-config-eb" class="dna-config-content space-y-3">
            <h4 class="text-purple-400 font-semibold text-xs mb-2">Configuration E.B</h4>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 4 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• S'il y a exactement deux G, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, si le premier nucléotide est T et qu'il y a au moins un C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il n'y a aucun A, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>second nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 5 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• Si le troisième nucléotide est C et qu'il y a plus d'un T, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a exactement trois A, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, si le dernier nucléotide est G et qu'il n'y a pas de T, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, s'il y a au moins deux C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 6 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• S'il y a plus de deux G et que le second nucléotide est A, répliquer le <strong>quatrième nucléotide</strong>.</p>
                <p>• Sinon, si le cinquième nucléotide est T et qu'il y a exactement un C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il n'y a aucun A et qu'il y a au moins un G, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, si le premier nucléotide est C, répliquer le <strong>cinquième nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>troisième nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 7 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• Si le quatrième nucléotide est A et qu'il y a exactement deux T, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a plus de trois G, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, si le sixième nucléotide est C et qu'il n'y a pas de A, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a au moins trois C et que le dernier nucléotide est T, répliquer le <strong>cinquième nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
              </div>
            </div>
          </div>

          {/* P.S Configuration */}
          <div id="dna-config-ps" class="dna-config-content hidden space-y-3">
            <h4 class="text-purple-400 font-semibold text-xs mb-2">Configuration P.S</h4>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 4 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• S'il n'y a aucun A, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, si le premier nucléotide est T et qu'il y a au moins un C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a exactement deux G, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>second nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 5 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• S'il y a au moins deux C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, si le troisième nucléotide est C et qu'il y a plus d'un T, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, si le dernier nucléotide est G et qu'il n'y a pas de T, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, s'il y a exactement trois A, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 6 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• Si le premier nucléotide est C, répliquer le <strong>cinquième nucléotide</strong>.</p>
                <p>• Sinon, s'il y a plus de deux G et que le second nucléotide est A, répliquer le <strong>quatrième nucléotide</strong>.</p>
                <p>• Sinon, s'il n'y a aucun A et qu'il y a au moins un G, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, si le cinquième nucléotide est T et qu'il y a exactement un C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>troisième nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 7 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• S'il y a au moins trois C et que le dernier nucléotide est T, répliquer le <strong>cinquième nucléotide</strong>.</p>
                <p>• Sinon, si le quatrième nucléotide est A et qu'il y a exactement deux T, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, si le sixième nucléotide est C et qu'il n'y a pas de A, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a plus de trois G, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
              </div>
            </div>
          </div>

          {/* V.T Configuration */}
          <div id="dna-config-vt" class="dna-config-content hidden space-y-3">
            <h4 class="text-purple-400 font-semibold text-xs mb-2">Configuration V.T</h4>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 4 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• Si le premier nucléotide est T et qu'il y a au moins un C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, s'il n'y a aucun A, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, s'il y a exactement deux G, répliquer le <strong>troisième nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 5 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• Si le dernier nucléotide est G et qu'il n'y a pas de T, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
                <p>• Sinon, s'il y a au moins deux C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, si le troisième nucléotide est C et qu'il y a plus d'un T, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a exactement trois A, répliquer le <strong>second nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 6 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• S'il n'y a aucun A et qu'il y a au moins un G, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>troisième nucléotide</strong>.</p>
                <p>• Sinon, si le cinquième nucléotide est T et qu'il y a exactement un C, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a plus de deux G et que le second nucléotide est A, répliquer le <strong>quatrième nucléotide</strong>.</p>
                <p>• Sinon, si le premier nucléotide est C, répliquer le <strong>cinquième nucléotide</strong>.</p>
              </div>
            </div>
            <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
              <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 7 nucléotides incluant le '?'</h3>
              <div class="text-gray-300 text-xs md:text-sm space-y-1">
                <p>• Si le sixième nucléotide est C et qu'il n'y a pas de A, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
                <p>• Sinon, s'il y a plus de trois G, répliquer le <strong>second nucléotide</strong>.</p>
                <p>• Sinon, si le quatrième nucléotide est A et qu'il y a exactement deux T, répliquer le <strong>premier nucléotide</strong>.</p>
                <p>• Sinon, s'il y a au moins trois C et que le dernier nucléotide est T, répliquer le <strong>cinquième nucléotide</strong>.</p>
              </div>
            </div>
          </div>
        </div>
        ) : (
        <div class="space-y-3">
          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 4 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• S'il n'y a pas de A, répliquer le <strong>second nucléotide</strong>.</p>
              <p>• Sinon, si le dernier nucléotide est C, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, s'il y a plus d'un G, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>dernier nucléotide</strong>.</p>
            </div>
          </div>

          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 5 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• S'il y a plus d'un A, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, si le dernier nucléotide est T et qu'il n'y a pas de A, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, s'il y a exactement un G, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, s'il y a plus d'un T, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>second nucléotide</strong>.</p>
            </div>
          </div>

          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 6 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• Si le dernier nucléotide est C, répliquer le <strong>quatrième nucléotide</strong>.</p>
              <p>• Sinon, s'il y a exactement un A et qu'il y a plus d'un T, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, s'il n'y a pas de C, répliquer le <strong>second nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>premier nucléotide</strong>.</p>
            </div>
          </div>

          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 7 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• S'il n'y a pas de T, répliquer le <strong>troisième nucléotide</strong>.</p>
              <p>• Sinon, s'il y a exactement un T et qu'il y a plus d'un C, répliquer le <strong>quatrième nucléotide</strong>.</p>
              <p>• Sinon, s'il n'y a pas de A, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
            </div>
          </div>
        </div>
        )}
      </div>
    </div>
  </div>
  
  <script>
    // Release machine when user leaves the page
    function releaseMachine() {
      navigator.sendBeacon('/api/machine/release', JSON.stringify({
        machineType: 'manual2'
      }));
    }

    // Handle page unload
    window.addEventListener('beforeunload', releaseMachine);
    window.addEventListener('pagehide', releaseMachine);
    
    // Handle navigation away (for SPAs)
    window.addEventListener('popstate', releaseMachine);
    
    // Tab functionality for nemo hostname DNA rules
    if (window.location.hostname === 'nemo') {
      document.addEventListener('DOMContentLoaded', function() {
        const dnaTabButtons = document.querySelectorAll('.dna-tab-button');
        const dnaConfigContents = document.querySelectorAll('.dna-config-content');
        
        dnaTabButtons.forEach(button => {
          button.addEventListener('click', function(event) {
            const target = event.currentTarget as HTMLButtonElement;
            const targetId = target.id.replace('dna-tab-', 'dna-config-');
            
            // Remove active class from all tabs
            dnaTabButtons.forEach(btn => {
              btn.classList.remove('active', 'bg-purple-600', 'text-white');
              btn.classList.add('bg-purple-800', 'text-gray-300');
            });
            
            // Add active class to clicked tab
            target.classList.add('active', 'bg-purple-600', 'text-white');
            target.classList.remove('bg-purple-800', 'text-gray-300');
            
            // Hide all config contents
            dnaConfigContents.forEach(content => content.classList.add('hidden'));
            
            // Show target config content
            const targetContent = document.getElementById(targetId);
            if (targetContent) {
              targetContent.classList.remove('hidden');
            }
          });
        });
      });
    }
  </script>
</Layout>
